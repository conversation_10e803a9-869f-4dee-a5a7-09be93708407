import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { 
  ArrowLeft, 
  Edit, 
  Download, 
  MoreHorizontal, 
  ChevronDown, 
  ChevronUp, 
  Calendar, 
  Clock, 
  BookOpen, 
  PenTool, 
  Target, 
  RotateCcw, 
  FileText 
} from 'lucide-react';
import type { Schedule, WeekSchedule } from '../types';
import { formatDate, sortWeeksByNumber } from '../utils/dateUtils';
import { exportAsText, exportAsMarkdown, exportAsJSON, printSchedule, generateStudySummary } from '../utils/exportUtils';

interface WeekCardProps {
  week: WeekSchedule;
  isExpanded: boolean;
  onToggle: () => void;
}

const WeekCard: React.FC<WeekCardProps> = ({ week, isExpanded, onToggle }) => {
  return (
    <div className="bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-700/50 overflow-hidden hover:border-purple-500/50 transition-all duration-300 transform hover:-translate-y-1">
      {/* Week Header */}
      <div 
        className="p-6 cursor-pointer hover:bg-gradient-to-r hover:from-purple-900/20 hover:to-blue-900/20 transition-all duration-200"
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl opacity-20 animate-pulse"></div>
              <div className="relative flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl text-white font-bold text-lg shadow-lg">
                {week.weekNumber}
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold text-white mb-1">
                Semana {week.weekNumber}
              </h3>
              <p className="text-sm text-purple-300 font-medium">
                {week.subject}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <span className="text-sm text-gray-400 font-medium">
              {isExpanded ? 'Recolher' : 'Expandir'}
            </span>
            <div className="p-2 rounded-lg bg-gray-700/50 hover:bg-gray-600/50 transition-colors duration-200">
              {isExpanded ? (
                <ChevronUp className="h-5 w-5 text-purple-400" />
              ) : (
                <ChevronDown className="h-5 w-5 text-purple-400" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Week Content */}
      {isExpanded && (
        <div className="border-t border-gray-700/50 p-6 bg-gradient-to-br from-gray-800/50 to-gray-900/50 animate-slide-up">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Topics */}
              <div className="bg-gray-700/50 rounded-xl p-4 backdrop-blur-sm">
                <h4 className="flex items-center text-sm font-bold text-white mb-4">
                  <BookOpen className="h-4 w-4 mr-2 text-purple-400" />
                  Tópicos de Estudo
                </h4>
                <ul className="space-y-3">
                  {week.topics.map((topic, index) => (
                    <li key={index} className="flex items-start">
                      <span className="inline-block w-2 h-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                      <span className="text-sm text-gray-300 leading-relaxed">{topic}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Essay Theme */}
              <div className="bg-gray-700/50 rounded-xl p-4 backdrop-blur-sm">
                <h4 className="flex items-center text-sm font-bold text-white mb-4">
                  <PenTool className="h-4 w-4 mr-2 text-blue-400" />
                  Tema da Redação
                </h4>
                <p className="text-sm text-gray-300 bg-blue-900/30 p-4 rounded-lg border border-blue-700/30 leading-relaxed">
                  {week.essayTheme}
                </p>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Questions Goal */}
              <div className="bg-gray-700/50 rounded-xl p-4 backdrop-blur-sm">
                <h4 className="flex items-center text-sm font-bold text-white mb-4">
                  <Target className="h-4 w-4 mr-2 text-green-400" />
                  Meta de Questões
                </h4>
                <div className="bg-green-900/30 p-4 rounded-lg border border-green-700/30">
                  <p className="text-sm text-gray-300">
                    <span className="font-bold text-green-300 text-lg">{week.questionsGoal.quantity} questões</span>
                  </p>
                  <p className="text-xs text-green-400 mt-1">
                    Matéria: {week.questionsGoal.subject}
                  </p>
                </div>
              </div>

              {/* Weekly Review */}
              <div className="bg-gray-700/50 rounded-xl p-4 backdrop-blur-sm">
                <h4 className="flex items-center text-sm font-bold text-white mb-4">
                  <RotateCcw className="h-4 w-4 mr-2 text-orange-400" />
                  Revisão da Semana
                </h4>
                <div className="bg-orange-900/30 p-4 rounded-lg border border-orange-700/30">
                  <p className="text-sm text-gray-300">
                    <span className="font-bold text-orange-300 text-lg">{week.weeklyReview.quantity} questões</span>
                  </p>
                  <p className="text-xs text-orange-400 mt-2 leading-relaxed">
                    {week.weeklyReview.description}
                  </p>
                </div>
              </div>

              {/* Suggested Exam */}
              <div className="bg-gray-700/50 rounded-xl p-4 backdrop-blur-sm">
                <h4 className="flex items-center text-sm font-bold text-white mb-4">
                  <FileText className="h-4 w-4 mr-2 text-red-400" />
                  Simulado Sugerido
                </h4>
                <div className="bg-red-900/30 p-4 rounded-lg border border-red-700/30">
                  <p className="text-sm font-bold text-red-300 mb-2">
                    {week.suggestedExam.name}
                  </p>
                  <div className="flex items-center justify-between text-xs text-red-400">
                    <span className="bg-red-800/30 px-2 py-1 rounded-full">Nível: {week.suggestedExam.level}</span>
                    <span className="bg-red-800/30 px-2 py-1 rounded-full">{week.suggestedExam.questionsCount} questões</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface ScheduleViewProps {
  schedule: Schedule;
  onBack: () => void;
  onEdit: () => void;
}

const ScheduleView: React.FC<ScheduleViewProps> = ({ schedule, onBack, onEdit }) => {
  const [expandedWeeks, setExpandedWeeks] = useState<Set<string>>(new Set());
  const [showExportMenu, setShowExportMenu] = useState(false);
  const exportMenuRef = useRef<HTMLDivElement>(null);

  // Close export menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportMenuRef.current && !exportMenuRef.current.contains(event.target as Node)) {
        setShowExportMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleWeek = (weekId: string) => {
    const newExpanded = new Set(expandedWeeks);
    if (newExpanded.has(weekId)) {
      newExpanded.delete(weekId);
    } else {
      newExpanded.add(weekId);
    }
    setExpandedWeeks(newExpanded);
  };

  const expandAll = () => {
    setExpandedWeeks(new Set(schedule.weeks.map(week => week.id)));
  };

  const collapseAll = () => {
    setExpandedWeeks(new Set());
  };

  const sortedWeeks = sortWeeksByNumber(schedule.weeks);

  return (
    <div className="max-w-6xl mx-auto animate-fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-500 rounded-2xl p-8 mb-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <button
              onClick={onBack}
              className="p-3 text-purple-100 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200 backdrop-blur-sm transform hover:scale-110"
            >
              <ArrowLeft className="h-6 w-6" />
            </button>
            <div>
              <h1 className="text-3xl font-bold mb-2 bg-gradient-to-r from-white to-purple-100 bg-clip-text text-transparent">
                {schedule.title}
              </h1>
              {schedule.description && (
                <p className="text-purple-100 text-lg">
                  {schedule.description}
                </p>
              )}
              <div className="flex items-center mt-4 space-x-6">
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-purple-200" />
                  <span className="text-purple-100 font-medium">{schedule.weeks.length} semana{schedule.weeks.length !== 1 ? 's' : ''}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-purple-200" />
                  <span className="text-purple-100 font-medium">Criado em {formatDate(new Date(schedule.createdAt))}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Export Menu */}
            <div className="relative" ref={exportMenuRef}>
              <button
                onClick={() => setShowExportMenu(!showExportMenu)}
                className="inline-flex items-center px-4 py-2 border border-white/20 text-sm font-medium rounded-xl text-white bg-white/10 hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white focus:ring-offset-purple-600 transition-all duration-200 backdrop-blur-sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Exportar
                <MoreHorizontal className="h-4 w-4 ml-2" />
              </button>

              {showExportMenu && (
                <div className="absolute right-0 mt-2 w-48 bg-gray-800/90 backdrop-blur-sm rounded-xl shadow-xl border border-gray-700/50 z-50">
                  <div className="py-2">
                    <button
                      onClick={() => {
                        exportAsText(schedule);
                        setShowExportMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700/50 hover:text-white transition-colors duration-200"
                    >
                      📄 Exportar como Texto
                    </button>
                    <button
                      onClick={() => {
                        exportAsMarkdown(schedule);
                        setShowExportMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700/50 hover:text-white transition-colors duration-200"
                    >
                      📝 Exportar como Markdown
                    </button>
                    <button
                      onClick={() => {
                        exportAsJSON(schedule);
                        setShowExportMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700/50 hover:text-white transition-colors duration-200"
                    >
                      💾 Exportar como JSON
                    </button>
                    <hr className="my-1 border-gray-600/50" />
                    <button
                      onClick={() => {
                        printSchedule(schedule);
                        setShowExportMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700/50 hover:text-white transition-colors duration-200"
                    >
                      🖨️ Imprimir
                    </button>
                    <button
                      onClick={() => {
                        const summary = generateStudySummary(schedule);
                        alert(summary);
                        setShowExportMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700/50 hover:text-white transition-colors duration-200"
                    >
                      📊 Ver Resumo
                    </button>
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={onEdit}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-xl text-purple-700 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white focus:ring-offset-purple-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Edit className="h-4 w-4 mr-2" />
              Editar Cronograma
            </button>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 mb-8 border border-gray-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={expandAll}
              className="text-sm text-purple-400 hover:text-purple-300 font-medium bg-purple-900/30 px-3 py-1 rounded-full transition-colors duration-200"
            >
              Expandir Todas
            </button>
            <span className="text-gray-600">|</span>
            <button
              onClick={collapseAll}
              className="text-sm text-purple-400 hover:text-purple-300 font-medium bg-purple-900/30 px-3 py-1 rounded-full transition-colors duration-200"
            >
              Recolher Todas
            </button>
          </div>
        </div>
      </div>

      {/* Weeks */}
      <div className="space-y-6">
        {sortedWeeks.map((week, index) => (
          <div key={week.id} className="animate-slide-up" style={{ animationDelay: `${index * 100}ms` }}>
            <WeekCard
              week={week}
              isExpanded={expandedWeeks.has(week.id)}
              onToggle={() => toggleWeek(week.id)}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ScheduleView;
