import type { StudySchedule, WeekSchedule, WeekFormData } from '../types';

// Generate unique ID
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// Format date to Brazilian format
export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Format date to short Brazilian format
export const formatDateShort = (date: Date): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  }).format(date);
};

// Convert form data to WeekSchedule
export const formDataToWeekSchedule = (formData: WeekFormData): WeekSchedule => {
  const now = new Date();

  return {
    id: generateId(),
    weekNumber: formData.weekNumber,
    subjects: formData.subjects,
    topics: formData.topics.split('\n').filter(topic => topic.trim() !== ''),
    essayTheme: formData.essayTheme,
    essayType: formData.essayType,
    essayInstructions: formData.essayInstructions,
    questionsGoal: {
      subject: formData.questionsGoal.subject,
      quantity: parseInt(formData.questionsGoal.quantity) || 0
    },
    weeklyReview: {
      description: formData.weeklyReview.description,
      quantity: parseInt(formData.weeklyReview.quantity) || 0
    },
    suggestedExam: {
      name: formData.suggestedExam.name,
      level: formData.suggestedExam.level,
      questionsCount: parseInt(formData.suggestedExam.questionsCount) || 0
    },
    createdAt: now,
    updatedAt: now
  };
};

// Convert WeekSchedule to form data
export const weekScheduleToFormData = (week: WeekSchedule): WeekFormData => {
  return {
    weekNumber: week.weekNumber,
    subjects: week.subjects || [],
    topics: week.topics.join('\n'),
    essayTheme: week.essayTheme,
    essayType: week.essayType || 'Dissertativa-argumentativa',
    essayInstructions: week.essayInstructions || '',
    questionsGoal: {
      subject: week.questionsGoal.subject,
      quantity: week.questionsGoal.quantity.toString()
    },
    weeklyReview: {
      description: week.weeklyReview.description,
      quantity: week.weeklyReview.quantity.toString()
    },
    suggestedExam: {
      name: week.suggestedExam.name,
      level: week.suggestedExam.level,
      questionsCount: week.suggestedExam.questionsCount.toString()
    }
  };
};

// Create empty form data
export const createEmptyFormData = (weekNumber: number = 1): WeekFormData => {
  return {
    weekNumber,
    subjects: [],
    topics: '',
    essayTheme: '',
    essayType: 'Dissertativa-argumentativa',
    essayInstructions: '',
    questionsGoal: {
      subject: '',
      quantity: ''
    },
    weeklyReview: {
      description: '',
      quantity: ''
    },
    suggestedExam: {
      name: '',
      level: 'Médio',
      questionsCount: ''
    }
  };
};

// Validate form data
export const validateFormData = (formData: WeekFormData): string[] => {
  const errors: string[] = [];

  if (!formData.subjects || formData.subjects.length === 0) {
    errors.push('Pelo menos uma matéria deve ser selecionada');
  }

  if (!formData.topics.trim()) {
    errors.push('Tópicos de estudo são obrigatórios');
  }

  if (!formData.essayTheme.trim()) {
    errors.push('Tema da redação é obrigatório');
  }

  if (!formData.essayType.trim()) {
    errors.push('Tipo de redação é obrigatório');
  }

  if (!formData.questionsGoal.subject.trim()) {
    errors.push('Matéria da meta de questões é obrigatória');
  }

  if (!formData.questionsGoal.quantity || parseInt(formData.questionsGoal.quantity) <= 0) {
    errors.push('Quantidade da meta de questões deve ser maior que zero');
  }

  if (!formData.weeklyReview.description.trim()) {
    errors.push('Descrição da revisão é obrigatória');
  }

  if (!formData.weeklyReview.quantity || parseInt(formData.weeklyReview.quantity) <= 0) {
    errors.push('Quantidade da revisão deve ser maior que zero');
  }

  if (!formData.suggestedExam.name.trim()) {
    errors.push('Nome do simulado é obrigatório');
  }

  if (!formData.suggestedExam.questionsCount || parseInt(formData.suggestedExam.questionsCount) <= 0) {
    errors.push('Número de questões do simulado deve ser maior que zero');
  }

  return errors;
};

// Sort schedules by creation date (newest first)
export const sortSchedulesByDate = (schedules: StudySchedule[]): StudySchedule[] => {
  return [...schedules].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

// Sort weeks by week number
export const sortWeeksByNumber = (weeks: WeekSchedule[]): WeekSchedule[] => {
  return [...weeks].sort((a, b) => a.weekNumber - b.weekNumber);
};

// Migrate old data format to new format (subject -> subjects)
export const migrateWeekSchedule = (week: any): WeekSchedule => {
  // Se já tem subjects, retorna como está
  if (week.subjects && Array.isArray(week.subjects)) {
    return week as WeekSchedule;
  }

  // Se tem subject (formato antigo), converte para subjects
  if (week.subject && typeof week.subject === 'string') {
    return {
      ...week,
      subjects: [week.subject],
      // Remove o campo antigo se existir
      subject: undefined
    } as WeekSchedule;
  }

  // Se não tem nem subject nem subjects, cria array vazio
  return {
    ...week,
    subjects: []
  } as WeekSchedule;
};

// Search schedules by title or description
export const searchSchedules = (schedules: StudySchedule[], query: string): StudySchedule[] => {
  if (!query.trim()) return schedules;

  const lowercaseQuery = query.toLowerCase();
  return schedules.filter(schedule =>
    schedule.title.toLowerCase().includes(lowercaseQuery) ||
    (schedule.description && schedule.description.toLowerCase().includes(lowercaseQuery)) ||
    schedule.weeks.some(week =>
      week.subjects.some(subject => subject.toLowerCase().includes(lowercaseQuery)) ||
      week.topics.some(topic => topic.toLowerCase().includes(lowercaseQuery))
    )
  );
};
