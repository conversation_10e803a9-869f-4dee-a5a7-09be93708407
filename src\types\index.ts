export interface WeekSchedule {
  id: string;
  weekNumber: number;
  subject: string;
  topics: string[];
  essayTheme: string;
  questionsGoal: {
    subject: string;
    quantity: number;
  };
  weeklyReview: {
    description: string;
    quantity: number;
  };
  suggestedExam: {
    name: string;
    level: string;
    questionsCount: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface StudySchedule {
  id: string;
  title: string;
  description?: string;
  weeks: WeekSchedule[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AppSettings {
  darkMode: boolean;
  language: 'pt-BR';
  defaultSubjects: string[];
}

export interface WeekFormData {
  weekNumber: number;
  subject: string;
  topics: string;
  essayTheme: string;
  questionsGoal: {
    subject: string;
    quantity: string;
  };
  weeklyReview: {
    description: string;
    quantity: string;
  };
  suggestedExam: {
    name: string;
    level: string;
    questionsCount: string;
  };
}
