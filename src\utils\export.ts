import type { StudySchedule, WeekSchedule } from '../types';
import { formatDateShort, sortWeeksByNumber } from './helpers';
import { APP_CONFIG } from './constants';

// Export schedule as text
export const exportAsText = (schedule: StudySchedule): void => {
  const sortedWeeks = sortWeeksByNumber(schedule.weeks);
  
  let content = `${APP_CONFIG.name}\n`;
  content += `${'='.repeat(50)}\n\n`;
  content += `CRONOGRAMA: ${schedule.title}\n`;
  
  if (schedule.description) {
    content += `DESCRIÇÃO: ${schedule.description}\n`;
  }
  
  content += `CRIADO EM: ${formatDateShort(new Date(schedule.createdAt))}\n`;
  content += `TOTAL DE SEMANAS: ${schedule.weeks.length}\n\n`;
  
  sortedWeeks.forEach((week, index) => {
    content += `${'='.repeat(30)}\n`;
    content += `SEMANA ${week.weekNumber} - ${week.subjects.join(', ').toUpperCase()}\n`;
    content += `${'='.repeat(30)}\n\n`;
    
    content += `📚 TÓPICOS DE ESTUDO:\n`;
    week.topics.forEach((topic, topicIndex) => {
      content += `   ${topicIndex + 1}. ${topic}\n`;
    });
    content += `\n`;
    
    content += `✍️ REDAÇÃO:\n`;
    content += `   Tema: ${week.essayTheme}\n`;
    if (week.essayType) {
      content += `   Tipo: ${week.essayType}\n`;
    }
    if (week.essayInstructions) {
      content += `   Instruções: ${week.essayInstructions}\n`;
    }
    content += `\n`;
    
    content += `🎯 META DE QUESTÕES:\n`;
    content += `   ${week.questionsGoal.quantity} questões de ${week.questionsGoal.subject}\n\n`;
    
    content += `🔄 REVISÃO DA SEMANA:\n`;
    content += `   ${week.weeklyReview.quantity} questões - ${week.weeklyReview.description}\n\n`;
    
    content += `📝 SIMULADO SUGERIDO:\n`;
    content += `   ${week.suggestedExam.name}\n`;
    content += `   Nível: ${week.suggestedExam.level}\n`;
    content += `   Questões: ${week.suggestedExam.questionsCount}\n\n`;
    
    if (index < sortedWeeks.length - 1) {
      content += `\n`;
    }
  });
  
  content += `\n${'='.repeat(50)}\n`;
  content += `Gerado por ${APP_CONFIG.name}\n`;
  content += `Criado por ${APP_CONFIG.author} - ${APP_CONFIG.authorUrl}\n`;
  
  downloadTextFile(content, `${schedule.title}.txt`);
};

// Export schedule as markdown
export const exportAsMarkdown = (schedule: StudySchedule): void => {
  const sortedWeeks = sortWeeksByNumber(schedule.weeks);
  
  let content = `# ${schedule.title}\n\n`;
  
  if (schedule.description) {
    content += `**Descrição:** ${schedule.description}\n\n`;
  }
  
  content += `**Criado em:** ${formatDateShort(new Date(schedule.createdAt))}\n`;
  content += `**Total de semanas:** ${schedule.weeks.length}\n\n`;
  content += `---\n\n`;
  
  sortedWeeks.forEach((week) => {
    content += `## Semana ${week.weekNumber} - ${week.subjects.join(', ')}\n\n`;
    
    content += `### 📚 Tópicos de Estudo\n\n`;
    week.topics.forEach((topic) => {
      content += `- ${topic}\n`;
    });
    content += `\n`;
    
    content += `### ✍️ Redação\n\n`;
    content += `**Tema:** ${week.essayTheme}\n\n`;
    if (week.essayType) {
      content += `**Tipo:** ${week.essayType}\n\n`;
    }
    if (week.essayInstructions) {
      content += `**Instruções:** ${week.essayInstructions}\n\n`;
    }
    
    content += `### 🎯 Meta de Questões\n\n`;
    content += `**${week.questionsGoal.quantity} questões** de ${week.questionsGoal.subject}\n\n`;
    
    content += `### 🔄 Revisão da Semana\n\n`;
    content += `**${week.weeklyReview.quantity} questões** - ${week.weeklyReview.description}\n\n`;
    
    content += `### 📝 Simulado Sugerido\n\n`;
    content += `- **Prova:** ${week.suggestedExam.name}\n`;
    content += `- **Nível:** ${week.suggestedExam.level}\n`;
    content += `- **Questões:** ${week.suggestedExam.questionsCount}\n\n`;
    
    content += `---\n\n`;
  });
  
  content += `*Gerado por ${APP_CONFIG.name}*\n\n`;
  content += `*Criado por [${APP_CONFIG.author}](${APP_CONFIG.authorUrl})*\n`;
  
  downloadTextFile(content, `${schedule.title}.md`);
};

// Export schedule as JSON
export const exportAsJSON = (schedule: StudySchedule): void => {
  const jsonContent = JSON.stringify(schedule, null, 2);
  downloadTextFile(jsonContent, `${schedule.title}.json`);
};

// Export all schedules as JSON
export const exportAllSchedulesAsJSON = (schedules: StudySchedule[]): void => {
  const exportData = {
    exportDate: new Date().toISOString(),
    appName: APP_CONFIG.name,
    version: APP_CONFIG.version,
    schedules: schedules
  };
  
  const jsonContent = JSON.stringify(exportData, null, 2);
  downloadTextFile(jsonContent, `cronogramas-backup-${formatDateShort(new Date()).replace(/\//g, '-')}.json`);
};

// Helper function to download text file
const downloadTextFile = (content: string, filename: string): void => {
  const element = document.createElement('a');
  const file = new Blob([content], { type: 'text/plain;charset=utf-8' });
  element.href = URL.createObjectURL(file);
  element.download = filename;
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
  URL.revokeObjectURL(element.href);
};

// Generate study summary
export const generateStudySummary = (schedule: StudySchedule): string => {
  const sortedWeeks = sortWeeksByNumber(schedule.weeks);
  
  const subjects = [...new Set(sortedWeeks.map(w => w.subject))];
  const totalQuestions = sortedWeeks.reduce((sum, week) => sum + week.questionsGoal.quantity, 0);
  const totalReviewQuestions = sortedWeeks.reduce((sum, week) => sum + week.weeklyReview.quantity, 0);
  const totalSimulatedQuestions = sortedWeeks.reduce((sum, week) => sum + week.suggestedExam.questionsCount, 0);
  
  let summary = `RESUMO DO CRONOGRAMA: ${schedule.title}\n\n`;
  summary += `📊 ESTATÍSTICAS GERAIS:\n`;
  summary += `• Total de semanas: ${schedule.weeks.length}\n`;
  summary += `• Matérias abordadas: ${subjects.length} (${subjects.join(', ')})\n`;
  summary += `• Total de questões para resolver: ${totalQuestions}\n`;
  summary += `• Total de questões de revisão: ${totalReviewQuestions}\n`;
  summary += `• Total de questões de simulados: ${totalSimulatedQuestions}\n`;
  summary += `• Total geral de questões: ${totalQuestions + totalReviewQuestions + totalSimulatedQuestions}\n\n`;
  
  summary += `📈 DISTRIBUIÇÃO POR MATÉRIA:\n`;
  subjects.forEach(subject => {
    const subjectWeeks = sortedWeeks.filter(w => w.subject === subject);
    const subjectQuestions = subjectWeeks.reduce((sum, week) => sum + week.questionsGoal.quantity, 0);
    summary += `• ${subject}: ${subjectWeeks.length} semana(s), ${subjectQuestions} questões\n`;
  });
  
  return summary;
};

// Print schedule
export const printSchedule = (schedule: StudySchedule): void => {
  const printWindow = window.open('', '_blank');
  if (!printWindow) return;
  
  const sortedWeeks = sortWeeksByNumber(schedule.weeks);
  
  let html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${schedule.title} - ${APP_CONFIG.name}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .week { margin-bottom: 30px; page-break-inside: avoid; }
        .week-title { background: #f0f0f0; padding: 10px; font-weight: bold; font-size: 18px; }
        .section { margin: 15px 0; }
        .section-title { font-weight: bold; color: #333; margin-bottom: 5px; }
        .topics { list-style-type: disc; margin-left: 20px; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc; font-size: 12px; color: #666; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${schedule.title}</h1>
        ${schedule.description ? `<p>${schedule.description}</p>` : ''}
        <p>Criado em: ${formatDateShort(new Date(schedule.createdAt))} | Total de semanas: ${schedule.weeks.length}</p>
      </div>
  `;
  
  sortedWeeks.forEach(week => {
    html += `
      <div class="week">
        <div class="week-title">Semana ${week.weekNumber} - ${week.subjects.join(', ')}</div>
        
        <div class="section">
          <div class="section-title">📚 Tópicos de Estudo:</div>
          <ul class="topics">
            ${week.topics.map(topic => `<li>${topic}</li>`).join('')}
          </ul>
        </div>
        
        <div class="section">
          <div class="section-title">✍️ Redação:</div>
          <p><strong>Tema:</strong> ${week.essayTheme}</p>
          ${week.essayType ? `<p><strong>Tipo:</strong> ${week.essayType}</p>` : ''}
          ${week.essayInstructions ? `<p><strong>Instruções:</strong> ${week.essayInstructions}</p>` : ''}
        </div>
        
        <div class="section">
          <div class="section-title">🎯 Meta de Questões:</div>
          <p>${week.questionsGoal.quantity} questões de ${week.questionsGoal.subject}</p>
        </div>
        
        <div class="section">
          <div class="section-title">🔄 Revisão da Semana:</div>
          <p>${week.weeklyReview.quantity} questões - ${week.weeklyReview.description}</p>
        </div>
        
        <div class="section">
          <div class="section-title">📝 Simulado Sugerido:</div>
          <p><strong>${week.suggestedExam.name}</strong><br>
          Nível: ${week.suggestedExam.level} | Questões: ${week.suggestedExam.questionsCount}</p>
        </div>
      </div>
    `;
  });
  
  html += `
      <div class="footer">
        <p>Gerado por ${APP_CONFIG.name} | Criado por ${APP_CONFIG.author}</p>
      </div>
    </body>
    </html>
  `;
  
  printWindow.document.write(html);
  printWindow.document.close();
  printWindow.focus();
  printWindow.print();
};
