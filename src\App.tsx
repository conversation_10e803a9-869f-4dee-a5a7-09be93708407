import { useState, useEffect } from 'react';
import type { StudySchedule, AppSettings } from './types';
import { STORAGE_KEYS, APP_CONFIG } from './utils/constants';
import { generateId, migrateWeekSchedule } from './utils/helpers';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Dashboard from './components/Dashboard';
import ScheduleForm from './components/ScheduleForm';
import ScheduleView from './components/ScheduleView';
import { useLocalStorage } from './hooks/useLocalStorage';

type ViewMode = 'dashboard' | 'create' | 'view' | 'edit';

function App() {
  const [schedules, setSchedules] = useLocalStorage<StudySchedule[]>(STORAGE_KEYS.SCHEDULES, []);
  const [settings, setSettings] = useLocalStorage<AppSettings>(STORAGE_KEYS.SETTINGS, {
    darkMode: true,
    language: 'pt-BR',
    defaultSubjects: []
  });
  
  const [currentView, setCurrentView] = useState<ViewMode>('dashboard');
  const [selectedSchedule, setSelectedSchedule] = useState<StudySchedule | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Apply dark mode (always enabled) and migrate data
  useEffect(() => {
    document.documentElement.classList.add('dark');
    // Force dark mode in settings if not already set
    if (!settings.darkMode) {
      setSettings(prev => ({ ...prev, darkMode: true }));
    }

    // Migrate old data format to new format
    const migratedSchedules = schedules.map(schedule => ({
      ...schedule,
      weeks: schedule.weeks.map(week => migrateWeekSchedule(week))
    }));

    // Update schedules if migration was needed
    const needsMigration = schedules.some(schedule =>
      schedule.weeks.some((week: any) => week.subject && !week.subjects)
    );

    if (needsMigration) {
      setSchedules(migratedSchedules);
    }
  }, []);

  const handleCreateSchedule = () => {
    setSelectedSchedule(null);
    setCurrentView('create');
    setSidebarOpen(false);
  };

  const handleEditSchedule = (schedule: StudySchedule) => {
    setSelectedSchedule(schedule);
    setCurrentView('edit');
    setSidebarOpen(false);
  };

  const handleViewSchedule = (schedule: StudySchedule) => {
    setSelectedSchedule(schedule);
    setCurrentView('view');
    setSidebarOpen(false);
  };

  const handleDeleteSchedule = (scheduleId: string) => {
    setSchedules(prev => prev.filter(s => s.id !== scheduleId));
    if (selectedSchedule?.id === scheduleId) {
      setCurrentView('dashboard');
      setSelectedSchedule(null);
    }
  };

  const handleDuplicateSchedule = (schedule: StudySchedule) => {
    const now = new Date();
    const duplicatedSchedule: StudySchedule = {
      ...schedule,
      id: generateId(),
      title: `${schedule.title} (Cópia)`,
      weeks: schedule.weeks.map(week => ({
        ...week,
        id: generateId(),
        createdAt: now,
        updatedAt: now
      })),
      createdAt: now,
      updatedAt: now
    };
    setSchedules(prev => [...prev, duplicatedSchedule]);
  };

  const handleSaveSchedule = (schedule: StudySchedule) => {
    if (currentView === 'edit' && selectedSchedule) {
      setSchedules(prev => prev.map(s => s.id === schedule.id ? schedule : s));
    } else {
      setSchedules(prev => [...prev, schedule]);
    }
    setCurrentView('dashboard');
    setSelectedSchedule(null);
  };



  const renderContent = () => {
    switch (currentView) {
      case 'create':
      case 'edit':
        return (
          <ScheduleForm
            schedule={selectedSchedule}
            onSave={handleSaveSchedule}
            onCancel={() => setCurrentView('dashboard')}
            isEditing={currentView === 'edit'}
          />
        );
      case 'view':
        return selectedSchedule ? (
          <ScheduleView
            schedule={selectedSchedule}
            onEdit={() => handleEditSchedule(selectedSchedule)}
            onBack={() => setCurrentView('dashboard')}
          />
        ) : null;
      default:
        return (
          <Dashboard
            schedules={schedules}
            onCreateSchedule={handleCreateSchedule}
            onEditSchedule={handleEditSchedule}
            onViewSchedule={handleViewSchedule}
            onDeleteSchedule={handleDeleteSchedule}
            onDuplicateSchedule={handleDuplicateSchedule}
          />
        );
    }
  };

  return (
    <div className="min-h-screen bg-[#0f172a] relative">
      {/* Header */}
      <Header
        onMenuClick={() => setSidebarOpen(!sidebarOpen)}
        onCreateSchedule={handleCreateSchedule}
      />

      {/* Sidebar - Fixed à esquerda */}
      <Sidebar
        isOpen={sidebarOpen}
        schedules={schedules}
        onSelectSchedule={handleViewSchedule}
        onCreateSchedule={handleCreateSchedule}
        onClose={() => setSidebarOpen(false)}
      />

      {/* Main Content - Centralizado com margem para sidebar */}
      <main className="ml-0 md:ml-64 transition-all duration-300 min-h-screen pt-20 pb-16">
        <div className="max-w-5xl mx-auto px-4 lg:px-6">
          {renderContent()}
        </div>
      </main>

      {/* Footer com créditos */}
      <footer className="fixed bottom-0 w-full bg-gray-900/80 backdrop-blur-sm border-t border-gray-700/50 py-2 z-30">
        <div className="text-center">
          <p className="text-sm text-gray-400">
            Criado por{' '}
            <a
              href={APP_CONFIG.authorUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-purple-400 hover:text-purple-300 transition-colors duration-200 font-medium"
            >
              {APP_CONFIG.author}
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
